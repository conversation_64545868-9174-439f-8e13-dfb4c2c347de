// 快速测试初始化路由的脚本 - 测试路由是否正常工作
import config from '../src/config.js';

const testAccounts = [
  {
    "id": 1,
    "email": "<EMAIL>",
    "password": "testpassword",
    "proofEmail": "<EMAIL>",
    "createDatetime": "2025-07-31 22:43:07",
    "resetStatus": 1,
    "resetDatetime": "2025-07-31 22:55:30",
    "resetFailMsg": "",
    "initStatus": 0,
    "initDatetime": "",
    "initFailMsg": ""
  }
];

async function testInitEndpoint() {
    try {
        console.log('Testing init endpoint...');
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
        
        const response = await fetch('http://localhost:3096/init-account', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.app.token}`
            },
            body: JSON.stringify(testAccounts),
            signal: controller.signal
        });

        clearTimeout(timeoutId);
        console.log('Response Status:', response.status);
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Route is working! Response received:');
            console.log('Success:', result.success);
            console.log('Message:', result.message);
            if (result.summary) {
                console.log('Summary:', result.summary);
            }
        } else {
            const errorText = await response.text();
            console.log('❌ Error Response:', errorText);
        }
    } catch (error) {
        if (error.name === 'AbortError') {
            console.log('⏰ Request timed out (this is expected for browser automation)');
            console.log('✅ Route is accessible and processing requests');
        } else {
            console.error('❌ Test failed:', error.message);
        }
    }
}

// 执行测试
testInitEndpoint();
